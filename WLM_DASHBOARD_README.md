# WLM Data Dashboard

A modern, dark-themed Streamlit dashboard for querying warehouse transaction data with multiple filter options.

## Features

### 🎨 Modern Dark Theme UI
- Professional dark theme with gradient backgrounds
- Responsive design with intuitive layout
- Custom styled buttons and components
- Real-time progress indicators

### 🔍 Advanced Filtering Options
- **Multi-select DC Filter**: Choose from 10 distribution centers
  - '29003', '29004', '59000', '79000', 'kr19007', 'kr19008', '49060', '49061', '29009', '29010'
- **Event Type Filter**: 
  - ACTUAL only
  - ESTIMATED only
  - BOTH (includes both 'ACTUAL' and 'ESTIMATED')
- **User Class Filter**:
  - "default" (no additional filter)
  - "FLT" (adds user_class filter)
- **Date Range Picker**: Select start and end dates

### 📊 Data Display & Analytics
- Real-time query execution with progress tracking
- Summary metrics (total records, warehouses, date range, data size)
- Interactive data table with adjustable row display
- SQL query preview in expandable section

### 💾 Export Options
- CSV download with custom filename
- Excel download with formatted sheets
- Automatic filename generation with date ranges

## Installation

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Database Configuration**
   - Copy `.env.example` to `.env`
   - Update with your actual database credentials
   - Configure all required database hosts and connection details

3. **Run the Application**
   ```bash
   streamlit run main.py
   ```

## Database Configuration

The dashboard requires PostgreSQL database connections. Configure the following environment variables in your `.env` file:

### Required Environment Variables
- `DB_HOST_04`, `DB_HOST_05`, `DB_HOST_06`, `DB_HOST_07`, `DB_HOST_08`: Database hosts
- `DB_PORT`: Database port (typically 5432)
- `DB_NAME_*`: Database names for each warehouse
- `DB_USER`: Database username
- `DB_PASSWORD`: Database password

## SQL Query Structure

The dashboard builds dynamic SQL queries based on your filter selections:

```sql
SELECT *
FROM mwlm.wlm_transaction wt
WHERE shift_date >= TO_DATE('start_date', 'dd-mm-yyyy')
    AND shift_date <= TO_DATE('end_date', 'dd-mm-yyyy')
    AND warehouse_id IN (selected_warehouses)
    AND event_type IN ('ACTUAL'|'ESTIMATED'|'ACTUAL','ESTIMATED')
    [AND user_class IN ('FLT')]  -- Optional based on filter
```

## Usage

1. **Select Filters**:
   - Choose one or more Distribution Centers
   - Select Event Type (ACTUAL, ESTIMATED, or BOTH)
   - Choose User Class filter (default or FLT)
   - Set date range

2. **Execute Query**:
   - Click "🚀 Execute Query" button
   - Monitor progress as data is retrieved from multiple databases

3. **Review Results**:
   - View summary metrics
   - Browse data in interactive table
   - Adjust number of displayed rows

4. **Download Data**:
   - Use CSV or Excel download buttons
   - Files include date range in filename

## Error Handling

- Database connection errors are handled gracefully
- Individual database failures don't stop the entire query
- Clear error messages and warnings for user guidance
- Input validation for date ranges and required selections

## Performance Features

- Progress tracking for multi-database queries
- Memory usage monitoring
- Configurable row display limits
- Efficient data concatenation from multiple sources

## Technical Architecture

- **Frontend**: Streamlit with custom CSS styling
- **Backend**: PostgreSQL connections via psycopg2
- **Data Processing**: Pandas for data manipulation
- **Export**: CSV and Excel format support
- **Configuration**: Environment-based database configuration

## Customization

The dashboard can be easily customized by:
- Modifying the `available_dcs` list for different warehouses
- Updating the SQL query structure in `build_wlm_query()`
- Adjusting the dark theme colors in the CSS styles
- Adding new filter options or export formats
