import streamlit as st
import pandas as pd
import os
from dotenv import load_dotenv
import warnings
from datetime import date
from io import BytesIO
import asyncpg
import asyncio

# Load environment variables
load_dotenv()
warnings.filterwarnings("ignore")

def get_db_params(db_list=None):
    """Get database parameters for asyncpg connections"""
    db_configs = {
        '04': ['29003', '29004'],
        '05': ['59000', '79000'], 
        '06': ['kr19007', 'kr19008'],
        '07': ['hg49016', 'ga49007', '49060', '49061'],
        '08': ['29009', '29010']
    }
    
    all_params = {}
    for host, dbs in db_configs.items():
        for db in dbs:
            if db_list and db not in db_list:
                continue
                
            all_params[db] = {
                "host": os.getenv(f"DB_HOST_{host}"),
                "port": int(os.getenv("DB_PORT", "5432")),
                "database": os.getenv(f"DB_NAME_{db}"),
                "user": os.getenv("DB_USER"),
                "password": os.getenv("DB_PASSWORD")
            }
    
    return all_params

async def execute_query_async(db_params, query):
    """Execute query asynchronously using asyncpg"""
    all_data = []
    
    async def query_single_db(db_name, params):
        try:
            conn = await asyncpg.connect(**params)
            try:
                rows = await conn.fetch(query)
                # Convert to list of dicts
                data = [dict(row) for row in rows]
                return db_name, data
            finally:
                await conn.close()
        except Exception as e:
            return db_name, f"Error: {str(e)}"
    
    # Execute all queries concurrently
    tasks = [query_single_db(db_name, params) for db_name, params in db_params.items()]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Process results
    for db_name, result in results:
        if isinstance(result, str) and result.startswith("Error"):
            st.warning(f"⚠️ {db_name}: {result}")
        elif isinstance(result, list):
            all_data.extend(result)
    
    return pd.DataFrame(all_data) if all_data else pd.DataFrame()

def run_async_query(db_params, query):
    """Wrapper to run async query in Streamlit"""
    try:
        # Create new event loop for Streamlit
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(execute_query_async(db_params, query))
        loop.close()
        return result
    except Exception as e:
        st.error(f"Async query failed: {str(e)}")
        return pd.DataFrame()

# Example usage:
def wlm_data_page_asyncpg():
    """WLM Data page using asyncpg"""
    st.title("🏭 WLM Data Dashboard (AsyncPG)")
    
    # Your existing UI code here...
    # When executing query:
    # db_params = get_db_params(selected_dcs)
    # query = build_wlm_query(start_date, end_date, selected_dcs, event_type, user_class)
    # all_data = run_async_query(db_params, query)
