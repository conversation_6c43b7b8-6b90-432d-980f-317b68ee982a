import streamlit as st
import pandas as pd
from pathlib import Path
from io import BytesIO

# fastexcel is kell csak nem szól 
import os
import polars as pl
import math
import shutil
import numpy as np
from datetime import date

# data input
directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd())
excel_inputs_f = "Stores_Inputs_2024_Q1_wo_prepacked_hours_fluct_cust_repaired_N.xlsx"

key_to_drg = pd.read_excel(directory / excel_inputs_f, "key_table_moduls")

stores = list(
    pl.read_excel(directory / excel_inputs_f, engine="calamine")["Store"]
        .unique()
)


def get_latest_folder(directory):
    # List all directories in the given directory
    all_directories = [os.path.join(directory, d) for d in os.listdir(directory) if
                       os.path.isdir(os.path.join(directory, d))]

    # Get the most recently modified directory
    latest_folder = max(all_directories, key=os.path.getmtime)

    return latest_folder


def get_latest_xlsx_file(directory, country):
    # List all files in the given directory, excluding hidden files and files starting with ~$
    all_files = [os.path.join(directory, f) for f in os.listdir(directory) if
                 not f.startswith('~$') and not f.startswith('.') and os.path.isfile(os.path.join(directory, f))]

    if country == 'HU':
        # Filter files to include only .xlsx files
        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx')]
    if country == 'CZ':
        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx') and f.__contains__('CZ.xlsx')]
    if country == 'SK':
        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx') and f.__contains__('SK.xlsx')]

    if not xlsx_files:
        return None  # No .xlsx files found

    # Get the most recently modified .xlsx file
    latest_xlsx_file = max(xlsx_files, key=os.path.getmtime)

    return latest_xlsx_file


# HU part
hu_path = r"/mnt/ce_srd_idr/REPORT/DB_Report_SRD_6462376/DB Report Hungary/"
latest_folder_hu = get_latest_folder(hu_path)
mods_hu_f = get_latest_xlsx_file(latest_folder_hu, 'HU')

sk_cz_path = r"/mnt/ce_srd_idr/REPORT/DB_Report_SRD_6462376/DB Report CZ  & SK/CZ&SK/"
latest_folder_sk_cz = get_latest_folder(sk_cz_path)
latest_folder_sk_cz = get_latest_folder(latest_folder_sk_cz)
mods_sk_f = get_latest_xlsx_file(latest_folder_sk_cz, 'SK')
mods_cz_f = get_latest_xlsx_file(latest_folder_sk_cz, 'CZ')




def Modul_numbers(key_to_drg, stores):
    
    
    
    
    
    print("\nCE Modul numbers downloading...\n")
    
    
    def read_xlsx_sheets(filepath):
        # Read all sheets of the Excel file into a dictionary of DataFrames
        xls = pd.ExcelFile(filepath)
        sheets = {}
        for sheet_name in xls.sheet_names:
            sheets[sheet_name] = pd.read_excel(xls, sheet_name)
        
        # Concatenate all DataFrames into a single DataFrame
        combined_df = pd.concat(sheets.values(), ignore_index=True)
    
        return combined_df
    
    def copy_excel_file(source, destination):
        shutil.copy(source, destination)
    
    # HU part
    hu_path = r"/mnt/ce_srd_idr/REPORT/DB_Report_SRD_6462376/DB Report Hungary/"
    latest_folder_hu = get_latest_folder(hu_path)
    mods_hu_f = get_latest_xlsx_file(latest_folder_hu, 'HU')
    print(f"\nHU file: {mods_hu_f}\n")
    mods_hu = read_xlsx_sheets(mods_hu_f)
    mods_hu = mods_hu.query("Status == 'Live'")

    mods_hu = mods_hu.groupby(['Store Number', 'Department (Desc1)', 'Display Group Code','Display Group Local Description', 'Display Group Description', ], as_index=False).agg({'Number of mods':'sum', 'Number of Products allocated':'sum'})

    mods_hu['Store Number'] = mods_hu['Store Number'].map(lambda x: str(4) + str(x)).astype("int")
    print(f"HU file contains: {mods_hu['Store Number'].nunique()} stores")
    dep_df = mods_hu[['Display Group Code', 'Department (Desc1)']].drop_duplicates()
    print("\nHU Modul numbers done!!\n")
    
    # SK CZ part
    sk_cz_path = r"/mnt/ce_srd_idr/REPORT/DB_Report_SRD_6462376/DB Report CZ  & SK/CZ&SK/"
    latest_folder_sk_cz = get_latest_folder(sk_cz_path)
    latest_folder_sk_cz = get_latest_folder(latest_folder_sk_cz)
    mods_sk_f = get_latest_xlsx_file(latest_folder_sk_cz, 'SK')
    print(f"\nSK file: {mods_sk_f}\n")
    mods_sk = read_xlsx_sheets(mods_sk_f)
    mods_sk['Store Number'] = mods_sk['Store Number'].map(lambda x: str(2) + str(x)).astype("int")
    
    print(f"SK file contains: {mods_sk['Store Number'].nunique()} stores\n")

    mods_cz_f = get_latest_xlsx_file(latest_folder_sk_cz, 'CZ')
    print(f"\nCZ file: {mods_cz_f}\n")
    mods_cz = read_xlsx_sheets(mods_cz_f)
    mods_cz['Store Number'] = mods_cz['Store Number'].map(lambda x: str(1) + str(x)).astype("int")
    print(f"CZ file contains: {mods_cz['Store Number'].nunique()} stores\n")
    mods_cz_sk = pd.concat([mods_sk, mods_cz])
    mods_cz_sk = mods_cz_sk[mods_cz_sk["Planogram Status"] == 'Live']
    mods_cz_sk = mods_cz_sk.groupby(['Store Number', 'Display Group Code', 'Display Group Description'], as_index=False).agg({'POG No Of Mods':'sum', 'Number of Products allocated':'sum'})
    mods_cz_sk.rename(columns={'POG No Of Mods':'Number of mods'}, inplace=True)
    mods_cz_sk = mods_cz_sk.merge(dep_df, on="Display Group Code", how='left')
    print("\nCZ&SK Modul numbers done!!\n")
    mods_ce = pd.concat([mods_hu, mods_cz_sk])
    mods_ce.rename(columns={'Display Group Code':'drg', 'Store Number':'store'}, inplace=True)
    
    print(f"CE total file contains: {mods_ce['store'].nunique()} stores\n")
    # # DRG mapping part
    # source_path = r"\\czprgvmfs03\CERange\CE RANGE\Mastersheet CE Range\Mastersheet stuff\DRG MAPPING.xlsx"
    # destination_path = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\Moduls"
    
    # copy_excel_file(source_path, destination_path)
    


    
    # Create a dictionary mapping drg_letter to department
    drg_dep_mapping = key_to_drg.set_index('drg_letter')['dep'].to_dict()
    
    drg_dep_mapping = {k: v for k, v in drg_dep_mapping.items() if not (isinstance(k, float) and math.isnan(k))}
    

    
    for drg_letter, dep in drg_dep_mapping.items():
        mods_ce.loc[mods_ce['drg'].str.startswith(drg_letter), 'dep'] = dep
    
    # OFD or PROMO or CAYG
    # mods_ce['dep'] = np.where((mods_ce['Merchandising Group Description'].str.contains('OFD'))
    #   | (mods_ce['Merchandising Group Description'].str.contains('PROMO'))
    #     |(mods_ce['Merchandising Group Description'].str.contains('CAYG')), 'PROMO_OFD_CAYG', mods_ce['dep'])
    
    # free form & healthy food to PRO
    mods_ce['dep'] = np.where((mods_ce['Display Group Description']=='FREE_FROM')
      | (mods_ce['Display Group Description']=='HEALTHY_FOOD'), 'PRO', mods_ce['dep'])
    
    
    mods_ce['country'] = ['CZ' if str(x).startswith('1') else 'SK' if str(x).startswith('2') else 'HU' for x in mods_ce['store']]
    

    mods_ce = mods_ce[mods_ce.store.isin(stores)]
    
    print(f"CE (with store list) file contains: {mods_ce['store'].nunique()} stores\n")

    mods_ce['dep'] = np.where(mods_ce['drg'] == 'P1A', 'NEW', mods_ce['dep'])
    
    # mods_ce.to_excel(
    #     place_to_save / f"{saved_filename}" / f"CE_Modul_nr_{saved_filename}.xlsx", index=False
    # )
    print("\nCE Modul numbers done!!\n")
    
    return mods_ce

def to_excel(df):
    output = BytesIO()
    writer = pd.ExcelWriter(output, engine='xlsxwriter')
    df.to_excel(writer, index=False, sheet_name='Sheet1')
    # workbook = writer.book
    # worksheet = writer.sheets['Sheet1']
    # format1 = workbook.add_format({'num_format': '0.00'})
    # worksheet.set_column('A:A', None, format1)
    writer.close()
    processed_data = output.getvalue()
    return processed_data


# Main Streamlit app
def main():
    st.set_page_config(
        page_title="Ce_Module_Numbers",
        page_icon=':bar_chart:'
        )

    st.title("Modul Numbers Downloader")

    with st.expander('Input Files', expanded=False):

        st.write(f":flag-hu: : {mods_hu_f}")
        st.write(f":flag-sk: : {mods_sk_f}")
        st.write(f":flag-cz: : {mods_cz_f}")
    
    # Button to run the script
    if st.button("Run Script"):
        with st.spinner("Running Script..."):
            df = Modul_numbers(key_to_drg, stores)
        st.success("Script execution complete!")

        df_xlsx = to_excel(df)
        st.download_button(
            label="Download data as Excel",
            data=df_xlsx,
            file_name=f'CE_modules_{date.today()}.xlsx',
            mime='application/vnd.ms-excel'
        )
    


if __name__ == "__main__":
    main()
    
    

