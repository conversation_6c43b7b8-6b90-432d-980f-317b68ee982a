import streamlit as st
import pandas as pd
from pathlib import Path
from io import BytesIO
import os
import polars as pl
import math
import shutil
import numpy as np
from datetime import date, datetime, timedelta


hu_path = r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report Hungary"  #r"/mnt/ce_srd_idr/REPORT/DB_Report_SRD_6462376/DB Report Hungary/" #r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report Hungary"
sk_cz_path = r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report CZ  & SK\CZ&SK" #r"/mnt/ce_srd_idr/REPORT/DB_Report_SRD_6462376/DB Report CZ  & SK/CZ&SK/" #r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report CZ  & SK\CZ&SK"

# data input
@st.cache_data
def load_initial_data():
    directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd())
    excel_inputs_f = "Repl_Stores_Inputs_2025_Q1_v6_GM and others_3 remaining stores.xlsx"
    
    key_to_drg = pd.read_excel(directory / excel_inputs_f, "key_table_moduls")

    stores = list(
        pl.read_excel(directory / excel_inputs_f, engine="calamine")["Store"]
            .unique()
    )
    
    return key_to_drg, stores

def extract_date_from_filename(filename):
    try:
        # Extract date from format like "WEEK_04_2025(24_03_2025)"
        if '(' in filename and ')' in filename:
            date_str = filename.split('(')[1].split(')')[0]
            return datetime.strptime(date_str, '%d_%m_%Y').date()
        # Extract year from folder name like "DB Report 2025"
        elif "DB Report " in filename:
            year = int(filename.split("DB Report ")[-1])
            return date(year, 1, 1)
        else:
            return datetime.fromtimestamp(os.path.getmtime(filename)).date()
    except (IndexError, ValueError):
        return datetime.fromtimestamp(os.path.getmtime(filename)).date()

def parse_month_folder_date(folder_name, year):
    """
    Parse month folder names to extract dates.
    Handles formats like 'July 3', '11 june', etc.
    Returns a date object or None if parsing fails.
    """
    import re

    # Month name mappings
    month_names = {
        'january': 1, 'jan': 1,
        'february': 2, 'feb': 2,
        'march': 3, 'mar': 3,
        'april': 4, 'apr': 4,
        'may': 5,
        'june': 6, 'jun': 6,
        'july': 7, 'jul': 7,
        'august': 8, 'aug': 8,
        'september': 9, 'sep': 9, 'sept': 9,
        'october': 10, 'oct': 10,
        'november': 11, 'nov': 11,
        'december': 12, 'dec': 12
    }

    try:
        folder_lower = folder_name.lower().strip()

        # Pattern 1: "July 3" or "july 3"
        match = re.search(r'([a-z]+)\s+(\d+)', folder_lower)
        if match:
            month_name = match.group(1)
            day = int(match.group(2))
            if month_name in month_names:
                month = month_names[month_name]
                return date(year, month, day)

        # Pattern 2: "11 june" or "11 June"
        match = re.search(r'(\d+)\s+([a-z]+)', folder_lower)
        if match:
            day = int(match.group(1))
            month_name = match.group(2)
            if month_name in month_names:
                month = month_names[month_name]
                return date(year, month, day)

        # Pattern 3: Just month name "june", "July"
        if folder_lower in month_names:
            month = month_names[folder_lower]
            return date(year, month, 1)

        # Pattern 4: Numeric format like "2025-07-03" or "07-03"
        if '-' in folder_lower:
            parts = folder_lower.split('-')
            if len(parts) == 3:  # YYYY-MM-DD
                return date(int(parts[0]), int(parts[1]), int(parts[2]))
            elif len(parts) == 2:  # MM-DD
                return date(year, int(parts[0]), int(parts[1]))

        return None
    except (ValueError, IndexError):
        return None

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_files_by_date(base_path, target_date):
    """Find folder closest to the target date"""
    try:
        all_folders = [os.path.join(base_path, d) for d in os.listdir(base_path) 
                      if os.path.isdir(os.path.join(base_path, d))]
        
        # Get dates for all folders
        folder_dates = [(folder, extract_date_from_filename(folder)) 
                       for folder in all_folders]
        
        if not folder_dates:
            return None
            
        # Calculate the difference between folder dates and target date
        folder_dates_with_diff = [(folder, folder_date, abs((folder_date - target_date).days)) 
                                 for folder, folder_date in folder_dates]
        
        # Sort by the difference (closest date first)
        folder_dates_with_diff.sort(key=lambda x: x[2])
        
        # Return the folder closest to the target date
        return folder_dates_with_diff[0][0]
    except Exception as e:
        print(f"Error in get_files_by_date: {e}")
        return None

def get_latest_xlsx_file(directory, country, target_date):
    """
    Get the Excel file closest to the target date for the specified country
    """
    if not directory or not os.path.exists(directory):
        return None

    # For CZ and SK, handle the specific path structure
    if country in ['CZ', 'SK'] and ('CZ&SK' in directory or 'CZ  & SK' in directory):
        try:
            base_dir = directory
            
            # Find the year folders
            year_folders = [os.path.join(base_dir, d) for d in os.listdir(base_dir) 
                           if os.path.isdir(os.path.join(base_dir, d)) and d.isdigit()]
            
            if not year_folders:
                print(f"No year folders found in {base_dir}")
                return None
            
            # Select appropriate year folder based on target_date
            year_folders_with_diff = []
            for folder in year_folders:
                folder_year = int(os.path.basename(folder))
                folder_date = date(folder_year, 1, 1)
                # Prioritize folders from the same year or earlier than target date
                if folder_date <= target_date:
                    year_diff = abs(folder_year - target_date.year)
                    year_folders_with_diff.append((folder, year_diff))
            
            if not year_folders_with_diff:
                # If no folders from target year or earlier, use the oldest available
                year_folders.sort()
                latest_year = year_folders[0]
            else:
                # Sort by year difference and use the closest year
                year_folders_with_diff.sort(key=lambda x: x[1])
                latest_year = year_folders_with_diff[0][0]
                
            print(f"Using year folder: {latest_year}")
            
            # Find month folders in the year folder
            month_folders = [os.path.join(latest_year, d) for d in os.listdir(latest_year)
                            if os.path.isdir(os.path.join(latest_year, d))]

            if not month_folders:
                print(f"No month folders found in {latest_year}")
                return None

            # Get dates for month folders using improved parsing
            month_folders_with_dates = []
            year_from_path = int(os.path.basename(latest_year))

            for folder in month_folders:
                folder_name = os.path.basename(folder)

                # Try to parse the folder name to get the actual date
                parsed_date = parse_month_folder_date(folder_name, year_from_path)

                if parsed_date:
                    folder_date = parsed_date
                    print(f"Parsed folder '{folder_name}' as date: {folder_date}")
                else:
                    # Fallback to modification time if parsing fails
                    folder_date = datetime.fromtimestamp(os.path.getmtime(folder)).date()
                    print(f"Using modification time for folder '{folder_name}': {folder_date}")

                # Calculate difference in days from target date
                days_diff = abs((folder_date - target_date).days)
                month_folders_with_dates.append((folder, folder_date, days_diff))

            # Sort by difference from target date, but prioritize newer dates when differences are similar
            # This ensures July 3, 2025 is preferred over June 11, 2025 when target date is recent
            month_folders_with_dates.sort(key=lambda x: (x[2], -x[1].toordinal()))
            
            # Print all month folders found for debugging
            print(f"Found {len(month_folders_with_dates)} month folders:")
            for folder, folder_date, days_diff in month_folders_with_dates:
                print(f"  - {os.path.basename(folder)}: {folder_date} (diff: {days_diff} days)")

            # Look for the country-specific file in each month folder, starting with the closest date
            for month_folder, folder_date, days_diff in month_folders_with_dates:
                print(f"Checking month folder: {os.path.basename(month_folder)} (Date: {folder_date}, diff: {days_diff} days)")
                files_in_folder = []

                try:
                    for file in os.listdir(month_folder):
                        # More flexible file matching - check if country code is in filename
                        if file.endswith('.xlsx') and not file.startswith('~$'):
                            # For CZ, look for files containing 'CZ' or files named 'CZ.xlsx'
                            # For SK, look for files containing 'SK' or files named 'SK.xlsx'
                            if (country == 'CZ' and ('CZ' in file.upper() or file.upper() == 'CZ.XLSX')) or \
                               (country == 'SK' and ('SK' in file.upper() or file.upper() == 'SK.XLSX')):
                                file_path = os.path.join(month_folder, file)
                                file_date = datetime.fromtimestamp(os.path.getmtime(file_path)).date()
                                days_diff_file = abs((file_date - target_date).days)
                                files_in_folder.append((file_path, file_date, days_diff_file))
                                print(f"    Found candidate file: {file} (Date: {file_date})")
                except PermissionError as e:
                    print(f"    Permission error accessing folder: {e}")
                    continue

                if files_in_folder:
                    # Sort files by date difference and return the closest one
                    files_in_folder.sort(key=lambda x: x[2])
                    closest_file = files_in_folder[0][0]
                    closest_date = files_in_folder[0][1]
                    print(f"Selected {country} file: {closest_file} (Date: {closest_date})")
                    return closest_file
            
            print(f"No {country} file found in any month folder")
            return None
            
        except Exception as e:
            print(f"Error finding {country} file: {str(e)}")
            return None
    
    # Logic for HU files
    all_files = [os.path.join(directory, f) for f in os.listdir(directory) 
                 if not f.startswith('~$') and not f.startswith('.') 
                 and os.path.isfile(os.path.join(directory, f))]
    
    if not all_files:
        return None

    # Filter files based on country and extension
    if country == 'HU':
        filtered_files = [f for f in all_files if f.lower().endswith('.xlsx')]
    else:
        return None

    # Create list of (file, date, date_diff) tuples
    files_with_dates = []
    for f in filtered_files:
        file_date = extract_date_from_filename(f)
        # Calculate difference in days from target date
        days_diff = abs((file_date - target_date).days)
        files_with_dates.append((f, file_date, days_diff))
    
    if not files_with_dates:
        return None

    # Sort by date difference to get the closest file to the target date
    files_with_dates.sort(key=lambda x: x[2])
    closest_file = files_with_dates[0][0]
    closest_date = files_with_dates[0][1]
    print(f"Selected file closest to target date: {closest_file} (Date: {closest_date})")
    
    return closest_file

# Rest of the code remains the same
def Modul_numbers(key_to_drg, stores, selected_date):
    print("\nCE Modul numbers downloading...\n")
    
    def read_xlsx_sheets(filepath):
        xls = pd.ExcelFile(filepath)
        sheets = {}
        for sheet_name in xls.sheet_names:
            sheets[sheet_name] = pd.read_excel(xls, sheet_name)
        combined_df = pd.concat(sheets.values(), ignore_index=True)
        return combined_df
    
    # Initialize variables to avoid reference errors
    mods_hu = None
    mods_sk = None
    mods_cz = None
    mods_hu_f = None
    mods_sk_f = None
    mods_cz_f = None
    dep_df = None
    
    # HU part
    latest_folder_hu = get_files_by_date(hu_path, selected_date)
    if latest_folder_hu:
        mods_hu_f = get_latest_xlsx_file(latest_folder_hu, 'HU', selected_date)
        print(f"\nHU file: {mods_hu_f}\n")
        if mods_hu_f:
            try:
                mods_hu = read_xlsx_sheets(mods_hu_f)
                mods_hu = mods_hu.query("Status == 'Live'")

                mods_hu = mods_hu.groupby(['Store Number', 'Department (Desc1)', 'Display Group Code',
                                          'Display Group Local Description', 'Display Group Description'],
                                         as_index=False).agg({'Number of mods':'sum',
                                                            'Number of Products allocated':'sum'})

                mods_hu['Store Number'] = mods_hu['Store Number'].map(lambda x: str(4) + str(x)).astype("int")
                print(f"HU file contains: {mods_hu['Store Number'].nunique()} stores")
                dep_df = mods_hu[['Display Group Code', 'Department (Desc1)']].drop_duplicates()
                print("\nHU Modul numbers done!!\n")
            except Exception as e:
                print(f"Error processing HU file: {str(e)}")
                return None
    
    # SK CZ part - directly check for the files in the specific path structure
    mods_sk_f = get_latest_xlsx_file(sk_cz_path, 'SK', selected_date)
    mods_cz_f = get_latest_xlsx_file(sk_cz_path, 'CZ', selected_date)
    
    if mods_sk_f:
        print(f"\nSK file: {mods_sk_f}\n")
        try:
            mods_sk = read_xlsx_sheets(mods_sk_f)
            mods_sk['Store Number'] = mods_sk['Store Number'].astype(str).str.replace(',', '')
            mods_sk['Store Number'] = mods_sk['Store Number'].map(lambda x: str(2) + str(x)).astype("int")
            print(f"SK file contains: {mods_sk['Store Number'].nunique()} stores\n")
        except Exception as e:
            print(f"Error processing SK file: {str(e)}")
            return None
    
    if mods_cz_f:
        print(f"\nCZ file: {mods_cz_f}\n")
        try:
            mods_cz = read_xlsx_sheets(mods_cz_f)
            mods_cz['Store Number'] = pd.to_numeric(mods_cz['Store Number'], errors='coerce')
            mods_cz = mods_cz[mods_cz['Store Number'].notna()]
            mods_cz['Store Number'] = mods_cz['Store Number'].astype(int)
            mods_cz['Store Number'] = mods_cz['Store Number'].map(lambda x: str(1) + str(x)).astype("int")
            print(f"CZ file contains: {mods_cz['Store Number'].nunique()} stores\n")
        except Exception as e:
            print(f"Error processing CZ file: {str(e)}")
            return None
    
    # Ensure we have data to process
    if not all([mods_hu is not None, mods_sk is not None, mods_cz is not None, dep_df is not None]):
        print("Missing one or more required data files")
        st.error("Could not find all required data files. Please check the network paths.")
        return None
    
    mods_cz_sk = pd.concat([mods_sk, mods_cz])
    mods_cz_sk = mods_cz_sk[mods_cz_sk["Planogram Status"] == 'Live']
    mods_cz_sk = mods_cz_sk.groupby(['Store Number', 'Display Group Code', 'Display Group Description'],
                                   as_index=False).agg({'POG No Of Mods':'sum',
                                                      'Number of Products allocated':'sum'})
    mods_cz_sk.rename(columns={'POG No Of Mods':'Number of mods'}, inplace=True)
    mods_cz_sk = mods_cz_sk.merge(dep_df, on="Display Group Code", how='left')
    print("\nCZ&SK Modul numbers done!!\n")
    
    mods_ce = pd.concat([mods_hu, mods_cz_sk])
    mods_ce.rename(columns={'Display Group Code':'drg', 'Store Number':'store'}, inplace=True)
    
    print(f"CE total file contains: {mods_ce['store'].nunique()} stores\n")
    
    # Create a dictionary mapping drg_letter to department
    drg_dep_mapping = key_to_drg.set_index('drg_letter')['dep'].to_dict()
    drg_dep_mapping = {k: v for k, v in drg_dep_mapping.items() 
                      if not (isinstance(k, float) and math.isnan(k))}
    
    for drg_letter, dep in drg_dep_mapping.items():
        mods_ce.loc[mods_ce['drg'].str.startswith(drg_letter), 'dep'] = dep
    
    mods_ce['country'] = ['CZ' if str(x).startswith('1') else 'SK' if str(x).startswith('2')
                         else 'HU' for x in mods_ce['store']]
    
    mods_ce = mods_ce[mods_ce.store.isin(stores)]
    
    print(f"CE (with store list) file contains: {mods_ce['store'].nunique()} stores\n")

    mods_ce['dep'] = np.where(mods_ce['drg'] == 'P1A', 'NEW', mods_ce['dep'])
    
    print("\nCE Modul numbers done!!\n")
    
    return mods_ce

def to_excel(df):
    output = BytesIO()
    writer = pd.ExcelWriter(output, engine='xlsxwriter')
    df.to_excel(writer, index=False, sheet_name='Sheet1')
    writer.close()
    processed_data = output.getvalue()
    return processed_data

def module_numbers_page():
    """Page function for Module Numbers functionality"""
    # Load initial data with caching
    key_to_drg, stores = load_initial_data()

    st.title("Modul Numbers Downloader")

    # Main container with border
    with st.container(border=True):
        # Add date selector
        selected_date = st.date_input(
            "Select Date",
            value=date.today(),
            min_value=date(2022, 1, 1),
            max_value=date.today()
        )

        # Get latest folders based on selected date
        latest_folder_hu = get_files_by_date(hu_path, selected_date)

        # Get files based on selected date
        mods_hu_f = get_latest_xlsx_file(latest_folder_hu, 'HU', selected_date) if latest_folder_hu else None

        # For CZ and SK, use the direct path structure
        mods_sk_f = get_latest_xlsx_file(sk_cz_path, 'SK', selected_date)
        mods_cz_f = get_latest_xlsx_file(sk_cz_path, 'CZ', selected_date)

        with st.expander('Input Files', expanded=False):
            if mods_hu_f:
                st.write(f":flag-hu: : {mods_hu_f}")
                file_date = datetime.fromtimestamp(os.path.getmtime(mods_hu_f)).strftime('%Y-%m-%d %H:%M:%S')
                st.write(f"Last modified: {file_date}")
                st.write(f"Days from selected date: {abs((datetime.strptime(file_date, '%Y-%m-%d %H:%M:%S').date() - selected_date).days)}")
            else:
                st.write(f":flag-hu: : No file found")

            if mods_sk_f:
                st.write(f":flag-sk: : {mods_sk_f}")
                file_date = datetime.fromtimestamp(os.path.getmtime(mods_sk_f)).strftime('%Y-%m-%d %H:%M:%S')
                st.write(f"Last modified: {file_date}")
                st.write(f"Days from selected date: {abs((datetime.strptime(file_date, '%Y-%m-%d %H:%M:%S').date() - selected_date).days)}")
            else:
                st.write(f":flag-sk: : No file found")

            if mods_cz_f:
                st.write(f":flag-cz: : {mods_cz_f}")
                file_date = datetime.fromtimestamp(os.path.getmtime(mods_cz_f)).strftime('%Y-%m-%d %H:%M:%S')
                st.write(f"Last modified: {file_date}")
                st.write(f"Days from selected date: {abs((datetime.strptime(file_date, '%Y-%m-%d %H:%M:%S').date() - selected_date).days)}")
            else:
                st.write(f":flag-cz: : No file found")

        # Centered start button
        _, col2, _ = st.columns([1, 1, 1])

        with col2:
            # Button to run the script
            if st.button("Run Script"):
                with st.spinner("Running Script..."):
                    df = Modul_numbers(key_to_drg, stores, selected_date)
                    if df is not None:
                        df = df.drop_duplicates()
                        st.success("Script execution complete!")

                        df_xlsx = to_excel(df)
                        st.download_button(
                            label="Download data as Excel",
                            data=df_xlsx,
                            file_name=f'CE_modules_{selected_date}.xlsx',
                            mime='application/vnd.ms-excel'
                        )
                    else:
                        st.error("Failed to generate data. Check the console for more details.")

def main():
    st.set_page_config(page_title="Replenishment Report", page_icon=":bar_chart:", layout="centered")

    # Import the WLM data page
    import wlm_data

    # Define pages with navigation structure
    pages = {
        "Data Processing": [
            st.Page(module_numbers_page, title="Module Numbers", icon=":material/analytics:"),
            st.Page(wlm_data.wlm_data_page, title="WLM Data Downloader", icon=":material/download:")
        ]
    }

    # Create navigation
    pg = st.navigation(pages, position="top")
    pg.run()

if __name__ == "__main__":
    main()