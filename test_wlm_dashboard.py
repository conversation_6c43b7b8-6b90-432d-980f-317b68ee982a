#!/usr/bin/env python3
"""
Test script for WLM Dashboard functionality
"""

import sys
import os
from datetime import date

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported"""
    try:
        import wlm_data
        print("✅ wlm_data module imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import wlm_data: {e}")
        return False

def test_remove_letters_function():
    """Test the remove_letters function"""
    try:
        from wlm_data import remove_letters
        
        # Test single item
        result1 = remove_letters(['29003'])
        expected1 = "('29003')"
        assert result1 == expected1, f"Expected {expected1}, got {result1}"
        print("✅ remove_letters single item test passed")
        
        # Test multiple items
        result2 = remove_letters(['29003', 'kr19007', '49060'])
        expected2 = "('29003', '19007', '49060')"
        assert result2 == expected2, f"Expected {expected2}, got {result2}"
        print("✅ remove_letters multiple items test passed")
        
        return True
    except Exception as e:
        print(f"❌ remove_letters function test failed: {e}")
        return False

def test_query_builder():
    """Test the SQL query builder function"""
    try:
        from wlm_data import build_wlm_query

        # Test basic query
        query = build_wlm_query(
            start_date="01-01-2025",
            end_date="31-01-2025",
            selected_dcs=['29003', '29004'],
            event_type="ACTUAL",
            user_class="default"
        )

        # Check that query contains expected elements
        assert "mwlm.wlm_transaction" in query
        assert "TO_DATE('01-01-2025'" in query
        assert "TO_DATE('31-01-2025'" in query
        assert "warehouse_id IN ('29003', '29004')" in query
        assert "event_type IN ('ACTUAL')" in query
        assert "user_class" not in query  # Should not be present for default

        print("✅ Basic query builder test passed")

        # Test BOTH event type
        query2 = build_wlm_query(
            start_date="01-01-2025",
            end_date="31-01-2025",
            selected_dcs=['29003'],
            event_type="BOTH",
            user_class="FLT"
        )

        assert "event_type IN ('ACTUAL', 'ESTIMATED')" in query2
        assert "user_class IN ('FLT')" in query2

        print("✅ Advanced query builder test passed")

        return True
    except Exception as e:
        print(f"❌ Query builder test failed: {e}")
        return False

def test_db_engines():
    """Test the database engine creation function"""
    try:
        from wlm_data import get_db_engines

        # Test with specific DCs (won't actually connect without env vars)
        engines = get_db_engines(['29003', '29004'])

        # Should return a dictionary
        assert isinstance(engines, dict)
        print("✅ Database engines function test passed")

        return True
    except Exception as e:
        print(f"❌ Database engines test failed: {e}")
        return False

def test_csv_function():
    """Test the CSV conversion function"""
    try:
        import pandas as pd
        from wlm_data import to_csv
        
        # Create test dataframe
        test_df = pd.DataFrame({
            'warehouse_id': ['29003', '29004'],
            'event_type': ['ACTUAL', 'ESTIMATED'],
            'shift_date': ['2025-01-01', '2025-01-02']
        })
        
        csv_data = to_csv(test_df)
        
        # Check that CSV data is bytes and contains expected content
        assert isinstance(csv_data, bytes)
        csv_str = csv_data.decode('utf-8')
        assert 'warehouse_id' in csv_str
        assert '29003' in csv_str
        assert 'ACTUAL' in csv_str
        
        print("✅ CSV conversion test passed")
        return True
    except Exception as e:
        print(f"❌ CSV conversion test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("🧪 Running WLM Dashboard Tests\n")
    
    tests = [
        ("Import Test", test_imports),
        ("Remove Letters Function", test_remove_letters_function),
        ("Query Builder", test_query_builder),
        ("Database Engines", test_db_engines),
        ("CSV Conversion", test_csv_function)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Dashboard is ready to use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
