import streamlit as st
import pandas as pd
import os
from dotenv import load_dotenv
import warnings
from datetime import date
from io import BytesIO
from sqlalchemy import create_engine, text
from urllib.parse import quote_plus

# Load environment variables
load_dotenv()
warnings.filterwarnings("ignore")

def get_db_engines(db_list=None):
    """
    Get SQLAlchemy engines for specified databases or all if none specified.
    db_list: List of database names e.g. ['29003', 'kr19007']
    """
    db_configs = {
        '04': ['29003', '29004'],
        '05': ['59000', '79000'], 
        '06': ['kr19007', 'kr19008'],
        '07': ['hg49016', 'ga49007', '49060', '49061'],
        '08': ['29009', '29010']
    }
    
    # Get all db engines
    all_engines = {}
    
    for host, dbs in db_configs.items():
        for db in dbs:
            if db_list and db not in db_list:
                continue
                
            # Build connection string
            host_val = os.getenv(f"DB_HOST_{host}")
            port = os.getenv("DB_PORT", "5432")
            database = os.getenv(f"DB_NAME_{db}")
            user = os.getenv("DB_USER")
            password = quote_plus(os.getenv("DB_PASSWORD", ""))
            
            if all([host_val, database, user]):
                connection_string = f"postgresql://{user}:{password}@{host_val}:{port}/{database}"
                all_engines[db] = create_engine(connection_string)
    
    return all_engines

def remove_letters(items):
    """Extract digits from warehouse IDs for SQL IN clause"""
    digits_only = (''.join(char for char in item if char.isdigit()) for item in items)
    digits_list = list(digits_only)
    
    if len(digits_list) == 1:
        return f"('{digits_list[0]}')"
    else:
        return str(tuple(digits_list))

def build_wlm_query(start_date, end_date, selected_dcs, event_type, user_class):
    """Build dynamic SQL query based on filter selections"""
    
    query = f"""
    SELECT *
    FROM mwlm.wlm_transaction wt
    WHERE shift_date >= TO_DATE('{start_date}', 'dd-mm-yyyy')
        AND shift_date <= TO_DATE('{end_date}', 'dd-mm-yyyy')
        AND warehouse_id IN {remove_letters(selected_dcs)}
    """
    
    if event_type == "BOTH":
        query += "    AND event_type IN ('ACTUAL', 'ESTIMATED')\n"
    else:
        query += f"    AND event_type IN ('{event_type}')\n"
    
    if user_class != "default":
        query += f"    AND user_class IN ('{user_class}')\n"
    
    return query

def execute_query_sqlalchemy(engines, query):
    """Execute query using SQLAlchemy engines"""
    all_data = pd.DataFrame()
    
    for db_name, engine in engines.items():
        try:
            with engine.connect() as conn:
                df = pd.read_sql_query(text(query), conn)
                all_data = pd.concat([all_data, df], ignore_index=True)
        except Exception as e:
            st.warning(f"⚠️ Error querying {db_name}: {str(e)}")
            continue
    
    return all_data

# Example usage in Streamlit app:
def wlm_data_page_sqlalchemy():
    """WLM Data page using SQLAlchemy"""
    st.title("🏭 WLM Data Dashboard (SQLAlchemy)")
    
    # Your existing UI code here...
    # When executing query:
    # engines = get_db_engines(selected_dcs)
    # query = build_wlm_query(start_date, end_date, selected_dcs, event_type, user_class)
    # all_data = execute_query_sqlalchemy(engines, query)
